<!--
 * @Description: 目标完成情况表格单元格内容组件
 * @Author: 周鹏武 Extracted from completion-table.vue
 -->
<template>
  <span :class="cellClass" @click="handleCellClick">
    {{ displayContent }}
  </span>
</template>

<script setup>
import { computed, inject } from 'vue'
import i18n from '@/lang'

// 定义组件名称
defineOptions({
  name: 'CellContent'
})

// 定义props
const props = defineProps({
  cellData: {
    type: [String, Number, Object],
    default: null
  },
  // 是否是百分比
  isPercent: {
    type: Boolean,
    default: false
  },
  columnKey: {
    type: String,
    required: true
  },
  throughData: {
    type: [String, Object],
    default: null
  }
})

// 定义emit
const emit = defineEmits(['cell-click'])

// 注入颜色类计算函数
const getColorClass = inject('getColorClass')

/**
 * 判断是否为签订人列
 * @returns {boolean} 是否为签订人列
 */
const isSignerColumn = computed(() => {
  return props.columnKey === i18n.t('chartAchievementSpecial.signer')
})

/**
 * 判断是否为成交额列
 * @returns {boolean} 是否为成交额列
 */
const isAmountColumn = computed(() => {
  return props.columnKey === i18n.t('chartAchievementSpecial.amount')
})

// /**
//  * 判断是否为成交量列
//  * @returns {boolean} 是否为成交量列
//  */
// const isVolumeColumn = computed(() => {
//   return props.columnKey === i18n.t('chartAchievementSpecial.volume')
// })
//
// /**
//  * 判断是否为未设置值
//  * @returns {boolean} 是否为未设置值
//  */
// const isNotSet = computed(() => {
//   return Number(props.cellData) === -1
// })

/**
 * @description 删除符号中的百分号, 并且转成数字
 * @param value
 */
function delPercentChar(value) {
  return Number(value.replace(/%/g, ''))
}

/**
 * 格式化后的单元格内容
 * @returns {string} 格式化后的内容
 */
const formattedContent = computed(() => {
  if (!props.cellData) {
    return '--'
  }

  // 如果是时间戳，转换为日期格式
  if (isSignerColumn.value && props.cellData.length === 13 && !isNaN(Number(props.cellData))) {
    const date = new Date(Number(props.cellData))
    return date.toLocaleDateString()
  }

  // 如果是数字字符串，尝试格式化
  if (isAmountColumn.value && !isNaN(Number(props.cellData))) {
    return Number(props.cellData).toLocaleString()
  }

  return props.cellData
})

/**
 * 计算单元格显示内容
 * @returns {string} 显示内容
 */
const displayContent = computed(() => {
  // 未设置或为空时
  if (!props.cellData) {
    return '--'
  }

  // 有值时返回格式化内容
  return formattedContent.value
})

/**
 * 计算单元格样式类
 * @returns {Object} 样式类对象
 */
const cellClass = computed(() => {
  // 如果没有数据
  if (!props.cellData) {
    return 'out-of-range'
  }

  // 如果有穿透链接
  if (props.throughData) {
    return 'finish'
  }

  // 如果是百分比列
  if (props.isPercent) {
    const numberData = delPercentChar(props.cellData)
    return getColorClass(numberData)
  }

  // 默认无特殊样式
  return ''
})

/**
 * 处理单元格点击事件
 */
function handleCellClick() {
  if (props.throughData) {
    emit('cell-click', {
      throughData: props.throughData,
      clickData: formattedContent.value
    })
  }
}
</script>

<style lang="scss" scoped>
// 颜色样式
.red {
  font-weight: 500;
  color: var(--error-color, $error-base-color-6);
}

.green {
  font-weight: 500;
  color: var(--success-color, $success-base-color-6);
}

.orange {
  font-weight: 500;
  color: var(--warning-color, $brand-color-5);
}

// 可点击元素样式
.finish {
  color: var(--link-color, $icon-blue);
  cursor: pointer;
  transition: opacity 0.2s ease;

  &:hover {
    text-decoration: underline;
    opacity: 0.9;
  }
}

// 不在考核范围样式
.out-of-range {
  font-style: italic;
  color: var(--disabled-color, $text-tip);
}
</style>
