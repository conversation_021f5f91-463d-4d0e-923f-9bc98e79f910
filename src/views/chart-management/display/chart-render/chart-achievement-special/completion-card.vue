<!--
 * @Description: 完成情况详情里的echarts卡片
 * @Author: 周鹏武 Optimized version
 -->

<template>
  <div class="completion-card">
    <!-- 仪表盘图表 -->
    <echarts
      ref="gaugeChart"
      :axis-line-width="15"
      class="completion-gauge"
      :data="gaugeData"
      :feature="{}"
      :gauge-color="gaugeColor"
      :graph-type="8"
      :is-system="true"
      :is-use-mock="false"
      :max="100"
      :show-detail-text="$t('chartAchievementSpecial.completionRate')"
    ></echarts>

    <!-- 内容区域 -->
    <div class="content">
      <!-- 标题 -->
      <div v-tooltip.top-start="card.name" class="content__title">
        {{ card.name }}
      </div>

      <!-- 完成值 -->
      <div class="complete">
        <div class="complete__label">
          {{ $t('chartAchievementSpecial.finishValue') + ':' }}
        </div>
        <span class="complete__value">{{ formattedFinishValue }}</span>
        <span class="complete__unit">{{ card.unit }}</span>
      </div>

      <!-- 目标值 -->
      <div class="complete">
        <div class="complete__label">
          {{ $t('chartAchievementSpecial.targetValue') + ':' }}
        </div>
        <template v-if="isTargetNotSet">
          <div class="complete__value complete__value--small">
            {{ $t('display.chartAchievement.notSet') }}
          </div>
        </template>
        <template v-else>
          <span class="complete__value complete__value--small">{{ formattedTargetValue }}</span>
          <span class="complete__unit">{{ card.unit }}</span>
        </template>
      </div>

      <!-- 未完成值 -->
      <div class="complete">
        <div class="complete__label">
          {{ $t('chartAchievementSpecial.unfinishedValue') + ':' }}
        </div>
        <span class="complete__value">{{ formattedUnfinishValue }}</span>
        <span class="complete__unit">{{ card.unit }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, nextTick } from 'vue'
import Echarts from '@/components/echarts/echarts.vue'
import { getPerformanceFinish } from '@/api/companyConfig'

/**
 * 完成情况卡片组件
 * 显示目标完成情况的仪表盘和详细数据
 */
// 定义组件名称
defineOptions({
  name: 'CompletionCard'
})
// 定义props
const props = defineProps({
  /**
   * 卡片数据
   */
  card: {
    type: Object,
    required: true
  },

  /**
   * 卡片索引
   */
  index: {
    type: Number,
    required: true
  },

  /**
   * 颜色配置
   */
  filterColor: {
    type: Object,
    default: () => ({
      performanceFinishDown: 20,
      performanceFinishUp: 80
    })
  }
})

// 解析目标值
function parseTargetValue(value) {
  if (!value || Number(value) === -1) {
    return -1
  }

  // 处理带逗号的数字字符串
  if (typeof value === 'string' && value.includes(',')) {
    return Number(value.replace(/,/g, ''))
  }

  return Number(value)
}

// 组件状态
const target = ref(parseTargetValue(props.card.target))
const rate = ref(Number(props.card.rate) || 0)
const gaugeColor = ref([])
const colorUpdateTimer = ref(null)
const gaugeChart = ref(null)

/**
 * 仪表盘数据
 * @returns {Array} 仪表盘数据数组
 */
const gaugeData = computed(() => {
  return [
    {
      value: Math.max(0, rate.value)
    }
  ]
})

/**
 * 目标是否未设置
 * @returns {boolean} 目标是否未设置
 */
const isTargetNotSet = computed(() => {
  return target.value === -1
})

/**
 * 格式化数值显示
 * @param {string|number} value - 要格式化的值
 * @returns {string} 格式化后的值
 */
function formatNumberValue(value) {
  if (value === null || value === undefined) return '--'

  // 保留原始格式，如果已经是格式化的字符串
  if (typeof value === 'string' && value.includes(',')) {
    return value
  }

  return value
}

/**
 * 格式化后的完成值
 * @returns {string} 格式化后的完成值
 */
const formattedFinishValue = computed(() => {
  return formatNumberValue(props.card.finish)
})

/**
 * 格式化后的目标值
 * @returns {string} 格式化后的目标值
 */
const formattedTargetValue = computed(() => {
  return formatNumberValue(props.card.target)
})

/**
 * 格式化后的未完成值
 * @returns {string} 格式化后的未完成值
 */
const formattedUnfinishValue = computed(() => {
  return formatNumberValue(props.card.unFinish)
})

/**
 * 初始化仪表盘颜色
 */
function initGaugeColors() {
  gaugeColor.value = [
    [props.filterColor.performanceFinishDown / 100, '#FD6865'], // 红色区域
    [props.filterColor.performanceFinishUp / 100, '#FEC069'], // 橙色区域
    [1, '#25CEBA'] // 绿色区域
  ]
}

/**
 * 处理窗口大小变化
 */
function handleResize() {
  if (gaugeChart.value) {
    nextTick(() => {
      // 触发图表重新渲染以适应新尺寸
      gaugeChart.value.$forceUpdate()
    })
  }
}

// 初始化和事件监听
onMounted(() => {
  // 添加窗口大小变化监听，优化图表响应式显示
  window.addEventListener('resize', handleResize)
})

// 清理事件监听和定时器
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)

  if (colorUpdateTimer.value) {
    clearTimeout(colorUpdateTimer.value)
    colorUpdateTimer.value = null
  }
})

/**
 * 获取完成率颜色配置
 * 使用防抖处理，避免频繁API调用
 */
function getFinishColor() {
  // 如果已经有定时器，先清除
  if (colorUpdateTimer.value) {
    clearTimeout(colorUpdateTimer.value)
  }

  // 设置300ms的防抖
  colorUpdateTimer.value = setTimeout(() => {
    getPerformanceFinish({})
      .then((res) => {
        if (res && res.result) {
          // 更新颜色区间
          updateGaugeColors(res.result)
        }
      })
      .catch((error) => {
        console.error('Failed to get performance finish colors:', error)
      })
      .finally(() => {
        colorUpdateTimer.value = null
      })
  }, 300)
}

/**
 * 更新仪表盘颜色
 * @param {Object} colorConfig - 颜色配置
 */
function updateGaugeColors(colorConfig) {
  // 检查值是否有变化，避免不必要的更新
  const downValue = colorConfig.performanceFinishDown / 100
  const upValue = colorConfig.performanceFinishUp / 100

  if (gaugeColor.value[0][0] !== downValue || gaugeColor.value[1][0] !== upValue) {
    gaugeColor.value[0][0] = downValue
    gaugeColor.value[1][0] = upValue
  }
}

// 初始化
initGaugeColors()
getFinishColor()
</script>

<style lang="scss" scoped>
.completion-card {
  display: flex;
  padding: 35px 0 35px 100px;
  margin-bottom: 16px;
  font-size: 12px;
  background: var(--card-bg-color, $base-white);
  border-radius: 6px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  // 仪表盘区域
  .completion-gauge {
    flex: 0 0 200px;
    height: 200px;
    margin-right: 80px;
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.02);
    }
  }

  // 内容区域
  .content {
    width: 180px;
    font-size: 16px;
    color: var(--text-primary, $text-main);

    // 标题样式
    &__title {
      position: relative;
      padding-bottom: 4px;
      margin-bottom: 16px;
      overflow: hidden;
      font-weight: 500;
      text-overflow: ellipsis;
      white-space: nowrap;

      &::after {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 40px;
        height: 2px;
        content: '';
        background-color: var(--primary-color, $brand-color-5);
      }
    }

    // 完成情况项
    .complete {
      margin-bottom: 20px;
      font-size: 14px;
      color: var(--text-secondary, $text-auxiliary);
      transition: transform 0.2s ease;

      &:hover {
        transform: translateX(2px);
      }

      &__value {
        font-size: 20px;
        font-weight: 500;
        color: var(--text-primary, $text-main);

        &--small {
          font-size: 18px;
        }
      }

      &__label {
        margin-bottom: 8px;
        font-weight: 400;
      }

      &__unit {
        margin-left: 4px;
        font-size: 14px;
        color: var(--text-secondary, $text-auxiliary);
      }
    }
  }

  // 响应式调整
  @media screen and (max-width: 768px) {
    flex-direction: column;
    align-items: center;
    padding: 20px;

    .completion-gauge {
      margin-right: 0;
      margin-bottom: 20px;
    }

    .content {
      width: 100%;
      text-align: center;

      &__title::after {
        left: 50%;
        transform: translateX(-50%);
      }
    }
  }
}
</style>
