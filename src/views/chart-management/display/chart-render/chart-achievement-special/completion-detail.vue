<!--
 * @Description: 目标完成情况详情页
 * @Author: 周鹏武 Optimized version
 -->
<template>
  <div class="completion-detail">
    <!-- 详情面板 - 滑动动画 -->
    <transition name="slide">
      <div v-if="detailShow" class="detail">
        <!-- 关闭按钮 -->
        <span class="detail-close" @click="closeDetail">
          <i class="el-icon-close"></i>
        </span>

        <div class="table-content">
          <!-- 标题和时间选择器 -->
          <div class="title">
            <!-- 标题文本 - 带溢出提示 -->
            <el-tooltip
              class="item"
              :content="targetPreview"
              :disabled="isShowTooltip"
              effect="dark"
              placement="top"
            >
              <div class="title-name" @mouseover="onMouseOver(targetPreview)">
                <span ref="targetRef">{{ targetPreview }}</span>
              </div>
            </el-tooltip>

            <!-- 时间选择器 -->
            <time-selector
              class="filter__time"
              :filter-param="timeFilterParam"
              :is-fiscal-year="true"
              pop-width="600"
              :set-default="false"
              @saveFilter="saveTime"
            >
              <span slot="title-lable">{{ $t('display.chartAchievement.timeRange') + ':' }}</span>
            </time-selector>
          </div>

          <!-- 卡片列表区域 -->
          <div v-if="!loading" class="finish-list">
            <template v-if="cards.length">
              <completion-card
                v-for="(item, index) in cards"
                :key="index"
                :card="item"
                :filter-color="filterColor"
                :index="index"
              />
            </template>

            <!-- 无数据提示 -->
            <div v-else class="no-data">
              {{ $t('chartAchievementSpecial.noData') }}
            </div>
          </div>

          <!-- 加载中状态 -->
          <div v-else class="loading-container">
            <el-skeleton animated :rows="3" />
          </div>
        </div>
      </div>
    </transition>

    <!-- 背景遮罩 - 淡入淡出动画 -->
    <transition name="fade">
      <div v-if="detailShow" class="detail-bg" @click="closeDetail"></div>
    </transition>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import CompletionCard from './completion-card'
import TimeSelector from '@/components/date-time/time-selector'
import { getPerformanceDetail } from '@/api/statistics.js'
import i18n from '@/lang'

/**
 * 完成情况详情组件
 * 显示目标完成情况的详细信息
 */
// 定义组件名称
defineOptions({
  name: 'CompletionDetail'
})
// 定义props
const props = defineProps({
  /**
   * 控制弹窗是否显示
   */
  detailShow: {
    type: Boolean,
    required: true
  },

  /**
   * 详情页的查询参数
   */
  detailParams: {
    type: Object,
    required: true
  },

  /**
   * 时间筛选参数
   */
  timeFilterParam: {
    type: Object,
    default: () => ({
      timeType: 3
    })
  },

  /**
   * 筛选参数
   */
  filterParam: {
    type: Object,
    default: () => ({})
  },

  /**
   * 颜色配置
   */
  filterColor: {
    type: Object,
    default: () => ({
      performanceFinishDown: 20,
      performanceFinishUp: 80
    })
  }
})

// 定义emit
const emit = defineEmits(['update:detailShow'])

// 组件状态
const cards = ref([]) // 卡片数据
const loading = ref(true) // 加载状态
const isShowTooltip = ref(false) // 是否显示提示
const targetRef = ref(null) // 标题引用

/**
 * 目标预览文本
 * @returns {string} 格式化的目标预览文本
 */
const targetPreview = computed(() => {
  return i18n.t('otherSetting.targetAchievementPreview', {
    attr: props.detailParams.name || ''
  })
})

/**
 * 重置数据
 */
function resetData() {
  cards.value = []
  loading.value = true
}

/**
 * 关闭详情面板
 */
function closeDetail() {
  emit('update:detailShow', false)
}

/**
 * 监听详情页打开时，获取详情信息
 */
watch(
  () => props.detailShow,
  (isShow) => {
    if (isShow) {
      resetData()
      getPerformanceDetailFunc()
    }
  },
  { immediate: true }
)

/**
 * 时间选择确定
 * @param {Object} param - 时间参数
 */
function saveTime(param) {
  getPerformanceDetailFunc(param)
}

/**
 * 获取完成情况详情
 * @param {Object} timeParam - 时间参数
 */
function getPerformanceDetailFunc(timeParam) {
  loading.value = true

  // 解构详情参数
  const { companyStructType, checkedDepId, checkedUserId, id, chartIdIn, statisticsType } =
    props.detailParams

  // 构建请求参数
  const param = {
    from: 'centerWeb',
    checkSubDep: props.filterParam.checkSubDep,
    companyStructType,
    checkedDepId,
    checkedUserId,
    timeFilter: timeParam || props.timeFilterParam,
    idIn: id ? [id] : [],
    chartIdIn,
    statisticsType
  }

  // 员工类型不需要checkSubDep参数
  if (param.companyStructType === 1) {
    delete param.checkSubDep
  }

  // 发起请求
  getPerformanceDetail(param)
    .then((res) => {
      if (res && res.result) {
        cards.value = res.result.perforData || []
      } else {
        cards.value = []
      }
    })
    .catch((error) => {
      console.error('Failed to get performance details:', error)
      cards.value = []
    })
    .finally(() => {
      loading.value = false
    })
}

/**
 * 鼠标悬停处理 - 检测内容是否溢出
 * @param {string} str - 引用名称
 */
function onMouseOver(str) {
  if (!targetRef.value) return

  const tag = targetRef.value
  // 获取元素父级可视宽度
  const parentWidth = tag.parentNode.offsetWidth
  // 获取元素可视宽度
  const contentWidth = tag.offsetWidth
  // 内容宽度小于等于父容器宽度时不显示tooltip
  isShowTooltip.value = contentWidth <= parentWidth
}
</script>

<style lang="scss" scoped>
@import '../../../../../styles/saas/detail.scss';
@import '../../../../../styles/object/detail.scss';

// 详情面板
.detail {
  position: relative;
  width: 600px;
  background: #e5e9f4;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

  // 关闭按钮
  .detail-close {
    position: absolute;
    top: 16px;
    right: 16px;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    font-size: 20px;
    color: $text-auxiliary;
    cursor: pointer;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transition: all 0.3s;

    &:hover {
      color: $text-main;
      background: rgba(255, 255, 255, 0.4);
    }
  }
}

// 内容区域
.table-content {
  display: flex;
  flex-direction: column;
  height: 100%;

  // 标题区域
  .title {
    display: flex;
    flex: none;
    align-items: center;
    justify-content: space-between;
    padding: 0 30px;
    font-size: 20px;
    color: $text-main;
    background: white;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);

    // 标题文本
    .title-name {
      overflow: hidden;
      line-height: 80px;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    // 时间选择器
    .time-selector {
      line-height: 40px;

      :deep(button) {
        border: none;
      }
    }
  }

  // 卡片列表
  .finish-list {
    flex: 1;
    padding: 10px;
    overflow: auto;
    scrollbar-width: thin;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.2);
      border-radius: 3px;
    }
  }

  // 无数据状态
  .no-data {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    font-size: 16px;
    color: $text-auxiliary;
  }

  // 加载状态
  .loading-container {
    padding: 20px;
  }
}

// 动画
.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter,
.slide-leave-to {
  transform: translateX(100%);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}
</style>
