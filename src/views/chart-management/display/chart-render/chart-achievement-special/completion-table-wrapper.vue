<!--
 * @Description: 目标完成情况
-->
<template>
  <div class="completion-table-wrapper" :class="{ 'dark-pagination': darkPagination }">
    <el-scrollbar style="height: 100%; padding: 20px">
      <!-- 头部 -->
      <div class="completion-box__head">
        <span class="completion-box__title">{{ chartExplain.name }}</span>
        <div class="actions-wrapper">
          <span
            v-for="(btn, index) in actionButtons"
            :key="index"
            v-tooltip.top="btn.label"
            class="action-btn"
            @click="btn.click"
          >
            <i :class="btn.icon" />
          </span>
        </div>
      </div>

      <!-- 筛选条件 -->
      <div class="completion-box__query">
        <completion-filter
          ref="completionFilterRef"
          :company-struct-type="objectiveType.key"
          :special-param="false"
          @sendFilter="getFilter"
        >
        </completion-filter>
      </div>
      <!-- 表格顶部指标 -->
      <div v-if="headerTarget && headerTarget.label" class="header-target__wrapper">
        <completion-target :header-target="headerTarget"> </completion-target>
      </div>
      <div class="completion-box__body">
        <!-- 表格 -->
        <completion-table
          :company-struct-type="objectiveType.key"
          :filter-form="filterForm"
          :loading="loading"
          :table-data="tableData"
          :table-title="tableTitle"
          @open-detail="openDetail"
        >
        </completion-table>

        <!-- 分页 -->
        <div v-if="pageOption" class="pagination-wrapper">
          <v-pagination
            :current-page.sync="pageOption.currentPageNum"
            layout="slot, sizes, prev, pager, next, jumper"
            :page-size.sync="pageOption.pageSize"
            :page-sizes="pageOption.pageSizes"
            :total="pageOption.rowsCount"
            @current-change="getCompletionList"
            @size-change="getCompletionList"
          >
          </v-pagination>
        </div>
      </div>
    </el-scrollbar>

    <penetrate
      v-if="clickPenetrate"
      fixed-api="getAchievementSpecialThrough"
      :is-report="false"
      :is-work-order-v2="false"
      :penetrate-visible.sync="clickPenetrate"
      :throught="penetrateParams"
      :use-fixed-api="true"
    ></penetrate>

    <operate-auth-special
      v-if="operateAuthVisible"
      :chart-id="chartExplain.chartId"
      :current-opportunity-template="chartExplain.filterParams"
      :operate-auth-visible.sync="operateAuthVisible"
      :show-statistical-rules="showStatisticalRules"
      :system-code="chartExplain.systemCode"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted, getCurrentInstance } from 'vue'
import completionFilter from './completion-filter'
import CompletionTarget from './completion-target'
import CompletionTable from './completion-table'
import { chartRelease, getSpecialPerformance, specialExport } from '@/api/statistics.js'
import { useStore } from '@/composables/useStore'

import systemChartStyleMixin from '@/views/chart-edit/ui-design/mixin/system-chart-style.js'
import Penetrate from '@/views/penetrate/index.vue'
import OperateAuthSpecial from '@/views/chart-management/display/chart-render/chart-achievement-special/operate-auth-special.vue'
import i18n from '@/lang'

// 定义组件名称
defineOptions({
  name: 'CompletionTableWrapper'
})

// 定义emit函数
const emit = defineEmits(['update:chartExplain'])

// 获取store实例
const store = useStore()

// 获取当前组件实例
const instance = getCurrentInstance()
const proxy = instance.proxy

// 从systemChartStyleMixin中获取customChartBackground
// const { customChartBackground } = systemChartStyleMixin.computed
const { darkPagination } = systemChartStyleMixin.computed

// 定义props
const props = defineProps({
  // 报表对象
  chartExplain: {
    type: Object,
    default: () => ({})
  },
  // 全局样式配置
  globalStyleOption: Object
})

// 目标完成情况类型；1为员工，2为部门
const objectiveType = reactive({
  name: i18n.t('display.chartAchievement.staffFinish'),
  key: 1
})
//
// const userOrDept = ref([
//   { name: i18n.t('display.chartAchievement.staffFinish'), key: 1 },
//   { name: i18n.t('display.chartAchievement.departmentFinish'), key: 2 }
// ])

const filterForm = reactive({
  // 筛选信息
  colorPreview: {} // 颜色信息
})

// 分页配置项
const pageOption = reactive({
  currentPageNum: 1, // 当前页的页码
  pageSize: 10, // 每页的大小
  pageSizes: [10, 20, 30, 40, 50, 100],
  rowsCount: 0 // 总的条数
})

const tableTitle = ref([]) // 表格的头部
const tableData = ref([]) // 表格的资源
const loading = ref(true)
const operateAuthVisible = ref(false)
const penetrateParams = ref('') //
// const detailShow = ref(false)
// 仪表盘时支持全屏
// const panelScreenShow = ref(false)
// 复制父容器 node
// const parentNode = ref('')
const headerTarget = ref({})
const clickPenetrate = ref(false)
const releaseShow = ref(false)
const completionFilterRef = ref(null)
const localPublishStatus = ref(props.chartExplain.publish)

// 显示离职人员开关配置
// const leaveMarkOption = reactive({
//   alias: 'performanceLeave',
//   performanceLeaveMarks: '1',
//   leaveMarkSwitchLoading: false
// })

// const numFormatVisible = ref(false) // 数值格式设置弹窗

// 从Vuex获取状态
// const waterMarkConfig = computed(() => store.getters.waterMarkConfig)
const isUiPaasHome = computed(() => store.getters.isUiPaasHome)

const showStatisticalRules = computed(() => {
  return props.chartExplain.systemCode === '10_01_03'
})

/**
 * 操作按钮配置
 */
const actionButtons = computed(() => {
  return [
    {
      label: i18n.t('operation.export'),
      click: excelExport,
      icon: 'web-icon-daochu web-iconfont'
    },

    {
      label:
        localPublishStatus.value === 0
          ? i18n.t('chartAchievementSpecial.publishToHome')
          : i18n.t('chartAchievementSpecial.cancelPublishToHome'),
      click: setPublishHome,
      icon:
        localPublishStatus.value === 0
          ? 'web-icon-publish web-iconfont'
          : 'web-icon-chexiao web-iconfont'
    },
    {
      label: i18n.t('chartAchievementSpecial.settings'),
      click: setOperateAuth,
      icon: 'el-icon-setting'
    }
  ]
})

/**
 * 获取图表权限
 * @returns {Object} 图表权限对象
 */
// const chartPermissions = computed(() => {
//   return utils.SS.get('biChartPermissions') || {}
// })

/**
 * 是否有图表更新权限
 * @returns {boolean} 是否有更新权限
 */
// const chartUpdate = computed(() => {
//   if (!proxy.$feeType.checkFeatureEnable('CHART.ShowNullData')) return false
//   return Boolean(chartPermissions.value?.chartUpdate)
// })

/**
 * 是否有菜单设置权限
 * @returns {boolean} 是否有菜单设置权限
 */
// const menuSetAble = computed(() => {
//   return Boolean(chartPermissions.value?.menuSetAble)
// })

/**
 * 当前组件的背景色/图片
 * @returns {Object} 样式对象
 */
// const systemSingleStyle = computed(() => {
//   if (!panelScreenShow.value) {
//     return { background: 'transparent' }
//   }
//
//   // 科技风由于背景是透明的，需要在全屏时添加黑色底
//   if (props.globalStyleOption && props.globalStyleOption.globalTheme === 3) {
//     return { background: '#323233' }
//   }
//
//   return customChartBackground.value
// })

/**
 * 监听目标类型变化，调整分页配置
 */
watch(
  () => objectiveType.key,
  (val) => {
    // 部门类型时，固定每页显示10条
    if (val === 2) {
      pageOption.pageSize = 10
      pageOption.pageSizes = [10]
    } else {
      // 员工类型时，提供多种分页选项
      pageOption.pageSizes = [10, 20, 30, 40, 50, 100]
    }
  }
)

// 组件挂载时保存父节点引用
onMounted(() => {})

/**
 * @description 处理时间范围筛选
 * @param paramObj
 */
function processDurationFilter(paramObj) {
  if (!props.chartExplain.search.includes(106)) {
    delete paramObj.durationFilter
  } else if (
    paramObj.durationFilter?.symbol &&
    !['empty', 'noempty'].includes(paramObj.durationFilter.symbol)
  ) {
    let durationFilterValidate = false

    if (paramObj.durationFilter.symbol === 'range') {
      durationFilterValidate =
        paramObj.durationFilter.value[0] !== undefined &&
        paramObj.durationFilter.value[1] !== undefined &&
        paramObj.durationFilter.value[1] > paramObj.durationFilter.value[0]
    } else {
      durationFilterValidate = paramObj.durationFilter.value[0] !== undefined
    }

    if (!durationFilterValidate) {
      delete paramObj.durationFilter
    }
  }
}

/**
 * @description 导出
 */
function excelExport() {
  const paramObj = [801].some((aa) => props.chartExplain.search.includes(aa))
    ? JSON.parse(JSON.stringify(props.chartExplain.filterParams))
    : JSON.parse(
        JSON.stringify(props.chartExplain.filterParams, function (key, value) {
          if (key === 'accountId') {
            // 判断是不是资金账户，不是资金账户去掉accountId参数
            return undefined
          } else {
            return value
          }
        })
      )
  processDurationFilter(paramObj)

  paramObj.idIn = [props.chartExplain.id]
  paramObj.statisticsType = props.chartExplain.statisticsType
  paramObj.chartIdIn = [
    {
      id: props.chartExplain.id,
      statisticsType: props.chartExplain.statisticsType,
      systemCode: props.chartExplain.systemCode
    }
  ]
  if (paramObj.checkedId.length > 0) {
    if (paramObj.checkedId[0].property === 'user') {
      paramObj.companyStructType = 1
      paramObj.checkedUserId = paramObj.checkedId[0].id
      paramObj.checkedDepId = ''
    } else {
      paramObj.companyStructType = 2
      paramObj.checkedDepId = paramObj.checkedId[0].id
      paramObj.checkedUserId = ''
    }
  } else {
    paramObj.checkedUserId = ''
    paramObj.checkedDepId = ''
    paramObj.companyStructType = 2
  }

  // paramObj.sortMap = xbb.deepClone(this.sortInfo)

  const params = {
    promise: specialExport(paramObj)
  }
  store.dispatch('proExport/startExpor', params)
}

/**
 * @description 设置操作权限
 */
function setOperateAuth() {
  operateAuthVisible.value = true
}

/**
 * @description 设置图表的发布状态
 */
// 创建本地状态来跟踪发布状态

// 监听props变化，更新本地状态
watch(
  () => props.chartExplain.publish,
  (newValue) => {
    localPublishStatus.value = newValue
  }
)

/**
 * @description 设置图表的发布状态
 */
function tempSetPublishStatus() {
  // 更新本地状态而不是直接修改prop
  localPublishStatus.value = Number(!localPublishStatus.value)
  // 通知父组件状态变化
  emit('update:chartExplain', { ...props.chartExplain, publish: localPublishStatus.value })
}

/**
 * @description 取消发布到首页
 */
function cancelPublish() {
  chartRelease({
    chartId: props.chartExplain.id,
    publish: localPublishStatus.value,
    statisticsType: props.chartExplain.statisticsType,
    systemCode: props.chartExplain.systemCode
  })
    .then((data) => {
      tempSetPublishStatus()
      proxy.$message({
        type: 'success',
        message: i18n.t('display.repealSuccess')
      })
    })
    .catch(() => {})
}

/**
 * @description 设置图表发布到首页
 */
function setPublishHome() {
  if (localPublishStatus.value === 0) {
    // UI-Paas模式，采用新的发布首页方法
    if (isUiPaasHome.value) {
      publishHomeForUiPaas()
      return
    }
    releaseShow.value = true
  } else {
    cancelPublish()
  }
}

/**
 * @description 开启了UI-PaaS的首页，发布到首页
 */
function publishHomeForUiPaas() {
  chartRelease({
    chartId: props.chartExplain.id,
    publish: localPublishStatus.value,
    statisticsType: props.chartExplain.statisticsType,
    systemCode: props.chartExplain.systemCode
  })
    .then((data) => {
      tempSetPublishStatus()
      proxy.$message({
        type: 'success',
        message: i18n.t('display.publishSuccess')
      })
    })
    .catch(() => {})
}

/**
 * 生成API请求参数
 * @param {boolean} isExport - 是否用于导出
 * @returns {Object} 请求参数对象
 */
function generateRequestParams(isExport = false) {
  const statisticsType = props.chartExplain.chartType === 27 ? 2 : 1

  const params = {
    page: pageOption.currentPageNum,
    pageSize: pageOption.pageSize,
    companyStructType: objectiveType.key,
    checkSubDep: filterForm.checkSubDep,
    timeFilter: filterForm.timeRange,
    checkedId: filterForm.checkedId,
    checkedDepId: filterForm.checkedDepId || '',
    statisticsType: isExport ? props.chartExplain.statisticsType : statisticsType,
    chartIdIn: [
      {
        id: props.chartExplain.id,
        statisticsType: isExport ? props.chartExplain.statisticsType : statisticsType,
        systemCode: props.chartExplain.systemCode
      }
    ]
  }

  // 员工类型不需要checkSubDep参数
  if (params.companyStructType === 1) {
    delete params.checkSubDep
  }

  // 选择部门目标时，部门id必传，默认组织架构的第一个部门id
  if (params.companyStructType === 2 && params.checkedDepId === '') {
    params.checkedDepId = 1
  }

  return params
}

/**
 * 获取列表信息
 */
function getCompletionList() {
  loading.value = true
  const params = generateRequestParams()

  // http://192.168.10.60:5555/pro/v1/dsValue/sys/result
  getSpecialPerformance(params)
    .then((res) => {
      const {
        title,
        data,
        pageHelper,
        headerTarget: headerTargetData
      } = res.result.chartList[0].table
      headerTarget.value = headerTargetData
      tableTitle.value = title || []
      tableData.value = data || []
      if (pageHelper) {
        Object.assign(pageOption, pageHelper)
      }
      loading.value = false
    })
    .catch(() => {
      loading.value = false
    })
}

/**
 * 筛选条件变化，重新请求列表，重置到首页
 * @param {Object} filter - 筛选条件
 */
function getFilter(filter) {
  Object.assign(filterForm, filter)
  pageOption.currentPageNum = 1
  getCompletionList()
}

/**
 * 目标改变时，重置为选择全公司的目标
 * @param {Object} val - 选中的目标类型
 */
// function handleCommand(val) {
//   if (val.key === objectiveType.key) {
//     return
//   }
//
//   Object.assign(objectiveType, val)
//   filterForm.checkedDepId = 1
//   filterForm.chartIdIn = []
//
//   // 范围选择清空
//   if (completionFilterRef.value) {
//     completionFilterRef.value.chooseName = []
//     completionFilterRef.value.filterForm.checkedId = []
//     completionFilterRef.value.filterForm.checkedDepId = ''
//     completionFilterRef.value.filterForm.checkedUserId = ''
//   }
// }

/**
 * @description 设置穿透页头部
 * @param {Object} payload - 穿透页头部信息
 */
function setPenetrateHeader(payload) {
  store.dispatch('setPenetrateHead', {
    backName: props.chartExplain.name,
    itemName: payload,
    filterPerson: ''
  })
}

/**
 * 打开详情页
 * @param {Object} payload - 详情参数
 */
function openDetail(payload) {
  const { throughData, clickData } = payload
  setPenetrateHeader(clickData)
  penetrateParams.value = throughData
  clickPenetrate.value = true
}
</script>

<style lang="scss" scoped>
@import './../../../../../styles/object/empty.scss';

// 禁止点击样式
.click-prevent {
  pointer-events: none;
}

// 全屏样式
#fullId {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2002;
  padding: 20px 10px;
  background: $base-white;
}

// 主容器样式
.completion-table-wrapper {
  position: relative;
  box-sizing: border-box;
  //min-height: 450px;
  //max-height: 600px;
  margin-bottom: 20px;
  background: #ffffff;

  .header-target__wrapper {
    display: flex;
    justify-content: center;
    width: 100%;
    height: 50px;
    margin-bottom: 20px;
    line-height: 50px;
  }

  // 数据延迟提示
  .custom-data-delay-tips {
    margin-bottom: 15px;
  }

  // 头部
  .completion-box__head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 36px;
    font-size: 16px;
    line-height: 36px;
    //background-color: $bg-primary;
    .completion-box__title {
      padding-left: 12px;
    }
    .actions-wrapper {
      display: flex;
      gap: 8px;
      justify-content: space-between;
      .action-btn {
        display: inline-block;
        width: 34px;
        height: 34px;
        font-size: 26px;
        line-height: 34px;
        text-align: center;
        cursor: pointer;
        border-radius: 2px;
        transition: all 0.3s;

        .web-icon-publish {
          vertical-align: super;
        }
        .web-icon-chexiao {
          vertical-align: super;
        }
        .web-icon-daochu {
          vertical-align: super;
        }
      }
      .action-btn:hover {
        background-color: $bg-primary;
      }
    }
  }

  // 查询区域
  .completion-box__query {
    box-sizing: border-box;
    height: 60px;
    overflow: hidden;
    line-height: 60px;
    //border-bottom: 1px solid $line-table;
  }

  // 内容区域
  .completion-box__body {
    display: flex;
    flex-direction: column;
    height: calc(100% - 96px);
  }

  // 仪表盘展示时头部样式
  .panel-head {
    width: calc(100% + 2px);
    height: 30px;
    margin-top: -2px;
    margin-left: -1px;

    &__title {
      font-size: 16px;
    }

    &__operate {
      position: absolute;
      top: 10px;
      right: 20px;
      display: none;

      .search-header_icons {
        display: inline-block;
        width: 24px;
        margin-right: 5px;
        line-height: 22px;
        text-align: center;
        cursor: pointer;
        box-shadow: 0px 0px 4px 0px rgba(32, 24, 24, 0.08), 0px 0px 4px 0px rgba(0, 0, 0, 0.07);
        transition: all 0.2s;

        i {
          font-size: 13px;
          color: $text-plain;
          transform: scale(0.8);
        }
      }
    }
  }

  // 悬停时显示操作区
  &:hover {
    .panel-head__operate {
      display: block;
    }
  }

  // 权限管理
  .authority-manage {
    display: inline-block;
    height: 36px;
    padding-right: 10px;
    font-size: 14px;
    line-height: 36px;
    text-align: right;

    &__leave-mark,
    &__publish,
    &__export,
    &__operation {
      display: inline-block;
      padding-left: 14px;
      cursor: pointer;
    }

    &__publish .web-icon-publish,
    &__operation .el-icon-setting,
    &__operation .web-iconfont {
      padding-right: 6px;
      font-size: 15px;
    }

    &__export .web-icon-daochu {
      padding-right: 4px;
      font-size: 15px;
      /* Icon for export functionality */
    }

    &__operation {
      padding-left: 17px;

      .el-icon-setting,
      .web-iconfont {
        font-size: 18px;
      }
    }

    &__text {
      height: 32px;
    }
  }

  // 分页
  .pagination-wrapper {
    box-sizing: border-box;
    height: 60px;
    padding: 15px 10px;
    text-align: right;
  }
}

// 对象选择
.object-choose {
  &__button {
    padding-left: 10px;
    cursor: pointer;
  }

  &__text {
    padding-right: 10px;
  }
}

// 激活状态
.object--active {
  color: $brand-color-5;
}
</style>
