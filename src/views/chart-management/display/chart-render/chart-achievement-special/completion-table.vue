<!--
 * @Description: 目标完成情况表格
 * @Author: 周鹏武 Optimized version
 -->
<template>
  <div class="completion-table">
    <el-table
      v-if="tableFinish"
      v-loading="loading"
      border
      :data="tableData"
      height="100%"
      max-height="550"
      :row-class-name="tableRowClassName"
    >
      <el-table-column v-if="tableTitle.length" type="index" width="50"> </el-table-column>
      <template v-for="(item, index) in tableTitle">
        <!-- 表格的部门名称和员工名称 -->
        <!-- 第一列（可点击的名称列） -->
        <el-table-column
          v-if="!index"
          :key="index"
          align="center"
          :label="item.titleValue"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <cell-content
              :cell-data="scope.row[index] && scope.row[index].value"
              :column-key="item.titleValue"
              :through-data="scope.row[index] && scope.row[index].through"
              @cell-click="handleNameClick"
            />
          </template>
        </el-table-column>

        <!-- 其他普通列 -->
        <el-table-column
          v-else
          :key="index"
          align="center"
          :label="item.titleValue"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <cell-content
              :cell-data="scope.row[index] && scope.row[index].value"
              :column-key="item.titleValue"
              :is-percent="!!item.isPercent"
              :through-data="scope.row[index] && scope.row[index].through"
            />
          </template>
        </el-table-column>
      </template>
    </el-table>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, provide } from 'vue'
// 导入抽取的单元格内容组件
import CellContent from './cell-content.vue'

// 定义组件名称
defineOptions({
  name: 'CompletionTable'
})
// 定义props
const props = defineProps({
  // 表格数据
  tableData: {
    type: Array,
    default: () => []
  },
  // 表头
  tableTitle: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: true
  },
  // 筛选对象
  filterForm: {
    type: Object,
    default: () => ({})
  },
  // 目标完成情况类型
  companyStructType: {
    type: Number,
    default: 1
  }
})

// 定义emit
const emit = defineEmits(['open-detail'])

// 组件状态
const tableFinish = ref(true)
// const mockThroughData = ref(
//   '{\n' +
//   '  "corpid": "xbb1dbf12a8b6e4492a881c3f0a1058a005",\n' +
//   '  "userId": "1713344873999",\n' +
//   '  "platform": "web",\n' +
//   '  "frontDev": "1",\n' +
//   '  "page": 1,\n' +
//   '  "pageSize": 20,\n' +
//   '  "checkedUserId": "1713344873999",\n' +
//   '  "timeType": 5,\n' +
//   '  "statisticsType": 1,\n' +
//   '  "companyStructType": 2,\n' +
//   '  "performancePk": false,\n' +
//   '  "systemCode": "4700_02_01",\n' +
//   '  "alias": "reportBoard",\n' +
//   '  "startTime": 1735660800,\n' +
//   '  "id": 40937195,\n' +
//   '  "endTime": 1767196800,\n' +
//   '  "businessType": 4700,\n' +
//   '  "categoryId": ***********\n' +
//   '}'
// )

/**
 * 获取颜色配置
 * @returns {Object} 颜色配置对象
 */
const colorConfig = computed(() => {
  const defaultConfig = {
    performanceFinishDown: 80,
    performanceFinishUp: 100
  }
  return props.filterForm.colorPreview || defaultConfig
})

/**
 * 获取表格文本应该显示的颜色样式
 * @param {number|string} value - 完成率值
 * @returns {string} CSS类名
 */
function getColorClass(value) {
  const numValue = Number(value)
  const { performanceFinishDown, performanceFinishUp } = colorConfig.value

  if (numValue < performanceFinishDown) {
    return 'red'
  } else if (numValue > performanceFinishUp) {
    return 'green'
  } else {
    return 'orange'
  }
}

// 提供getColorClass方法给子组件
provide('getColorClass', getColorClass)

/**
 * 刷新表格 - 通过临时移除表格并重新渲染来解决某些渲染问题
 */
function refreshTable() {
  tableFinish.value = false
  nextTick(() => {
    tableFinish.value = true
  })
}

// 监听表头变化，刷新表格
watch(
  () => props.tableTitle,
  () => {
    refreshTable()
  },
  { deep: true }
)

/**
 * 设置表格行的类名
 * @param {Object} params - 行参数对象
 * @returns {string} 行类名
 */
function tableRowClassName({ rowIndex }) {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row'
}

/**
 * 处理名称点击事件
 * @param {Object} payload - 打开详情列表的参数
 */
function handleNameClick(payload) {
  emit('open-detail', payload)
}

/**
 * 处理目标跳转
 * @param {string} payload - 打开详情列表的参数
 */
// function linkToTargetThrough(payload) {
// try {
//   window.open(url, '_blank')
// } catch (error) {
//   console.error('Failed to open link:', error)
// }
// }
</script>

<style lang="scss" scoped>
.completion-table {
  box-sizing: border-box;
  flex: 1;
  height: calc(100% - 60px);
  min-height: 450px;
  max-height: 600px;
  // 可点击元素样式 - 仅用于名称列按钮
  .allow-click {
    color: var(--link-color, $icon-blue);
    cursor: pointer;
    transition: opacity 0.2s ease;

    &:hover {
      text-decoration: underline;
      opacity: 0.9;
    }
  }

  // 表格行交替颜色
  .even-row {
    background-color: var(--table-even-row-color, rgba(250, 250, 250, 0.5));
  }

  .odd-row {
    background-color: var(--table-odd-row-color, rgba(255, 255, 255, 1));
  }

  :deep(.el-table--fluid-height::before) {
    display: none;
  }

  // 表格样式优化
  :deep(.el-table) {
    .el-table__header-wrapper th {
      font-weight: 500;
      color: var(--table-head-text-color, $text-main);
      background-color: var(--table-head-color, $bg-blue);
    }

    .el-table__body-wrapper td {
      color: var(--table-content-text-color, $text-main);
      background-color: transparent; // 使用行背景色
    }

    // 表格行悬停效果
    tr:hover > td {
      background-color: var(--table-hover-color, rgba(249, 249, 249, 0.5)) !important;
    }

    // 表格边框和分割线
    .el-table__row,
    .el-table__header {
      td,
      th {
        border-bottom: 1px solid var(--table-border-color, rgba(235, 238, 245, 1));
      }
    }

    // 空数据状态
    .el-table__empty-text {
      font-size: 14px;
      color: var(--text-secondary, $text-auxiliary);
    }
  }
}
</style>
