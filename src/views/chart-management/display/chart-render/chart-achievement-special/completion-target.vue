<script setup>
defineOptions({
  name: 'CompletionTarget'
})
defineProps({
  headerTarget: {
    type: Object,
    default: () => ({
      label: '',
      value: ''
    })
  }
})
</script>

<template>
  <div class="completion-target">
    <span class="header-target__label">
      {{ headerTarget.label }}
    </span>
    <span class="header-target__value"> {{ headerTarget.value }}% </span>
  </div>
</template>

<style lang="scss" scoped>
.completion-target {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 300px;
  height: 50px;
  padding: 10px 0;
  font-size: 16px;
  line-height: 50px;
  border: 1px solid $line-table;
  .header-target__label {
    display: inline-block;
    width: 60%;
    margin-right: 10px;
    font-size: 14px;
    color: $text-main;
    text-align: center;
  }
  .header-target__value {
    display: inline-block;
    width: 40%;
    color: $text-main;
    text-align: center;
    border-left: 1px dashed $line-table;
  }
}
</style>
