<template>
  <div class="chart-achievement-special">
    <div v-if="chartList.length && selectMenu.alias === 'employeePerformanceAnalysis'" class="completion-box">
      <completion-table-wrapper
        v-for="(item, index) in chartList"
        :key="index"
        :chart-explain="item"
        :global-style-option="globalStyleOption"
      >
      </completion-table-wrapper>
    </div>
    <template v-else-if="selectMenu.alias === 'customerAssetInventory'">
      <customer-asset-inventory />
    </template>
    <div v-else class="empty">
      <img class="empty__img" src="@/assets/empty.png" />
      <span class="empty__hint">{{ $t('display.noChart') }}</span>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import CompletionTableWrapper from './completion-table-wrapper.vue'
import CustomerAssetInventory
  from '@/views/chart-management/display/chart-render/chart-achievement-special/customer-asset-inventory.vue'

// 定义组件名称
defineOptions({
  name: 'ChartAchievementSpecial'
})
// 定义props
const props = defineProps({
  // 报表对象
  chartOption: {
    type: Object,
    default: () => ({})
  },
  // 选中的分组对象
  selectMenu: {
    type: Object,
    default: () => ({})
  },
  // 全局样式配置
  globalStyleOption: Object
})

// 计算属性
const chartList = computed(() => {
  return props.chartOption.chartList || []
})
</script>

<style lang="scss" scoped>
.chart-achievement-special {
  position: relative;
  width: 100%;
  height: 100%;
  .completion-box {
    width: 100%;
    height: 100%;
    overflow-x: scroll;
  }
}
</style>
