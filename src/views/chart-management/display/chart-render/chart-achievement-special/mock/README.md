# 图表管理 Mock 数据

这个目录包含了用于调试图表管理相关组件的 Mock 数据和相关工具函数。

## 文件说明

### `customer-categories-data.js`
客户分类红灯占比 Mock 数据文件，包含：
- 基础客户分类数据
- 按风险等级和类型分组的数据
- 统计信息
- 工具函数

### `customer-assets-data.js`
员工客户资产 Mock 数据文件，包含：
- 员工基础信息
- 客户资产详情
- 跟进状态管理
- 风险等级分类
- 统计分析数据

### `customer-assets-usage-example.js`
员工客户资产使用示例文件，展示如何在不同场景下使用这些数据

### `usage-example.js`
客户分类数据使用示例文件

## 数据结构

### 客户分类数据格式
```javascript
{
  categoryId: "1001",           // 分类ID
  categoryLabel: "A类红灯客户占比", // 分类标签
  categoryCustomerCount: 80,     // 该分类总客户数
  redCustomerCount: 55,         // 红灯客户数
  redCustomerRatio: 68.75,      // 红灯客户占比
  categoryType: "level",        // 分类类型
  riskLevel: "high"            // 风险等级
}
```

### 员工客户资产数据格式
```javascript
{
  employeeId: "EMP001",         // 员工ID
  employeeName: "张三",         // 员工姓名
  department: "销售一部",       // 所属部门
  position: "高级销售经理",     // 职位
  totalCustomers: 45,           // 总客户数
  redLightCustomers: 8,         // 红灯客户数
  redLightRatio: 17.78,         // 红灯客户占比
  lastUpdateTime: "2024-01-15 14:30:00", // 最后更新时间
  assets: [                     // 客户资产列表
    {
      assetId: "ASSET001",      // 资产ID
      companyName: "北京科技有限公司", // 公司名称
      contactPerson: "李经理",   // 联系人
      contactPhone: "138****8888", // 联系电话
      followUpStatus: "danger",  // 跟进状态 (normal/warning/danger)
      daysNotFollowed: 12,      // 未跟进天数
      lastFollowUpDate: "2024-01-03", // 最后跟进日期
      customerLevel: "A",       // 客户等级
      assetValue: 850000,       // 资产价值
      potentialValue: 1200000,  // 潜在价值
      riskLevel: "high"         // 风险等级
    }
  ]
}
```

### 统计信息格式
```javascript
{
  totalEmployees: 3,            // 员工总数
  totalCustomers: 105,          // 客户总数
  totalRedLightCustomers: 16,   // 红灯客户总数
  averageRedLightRatio: 14.71,  // 平均红灯客户占比
  totalAssetValue: 3400000,     // 总资产价值
  totalPotentialValue: 4930000, // 总潜在价值
  highRiskAssetsCount: 3,       // 高风险资产数量
  mediumRiskAssetsCount: 2,     // 中风险资产数量
  lowRiskAssetsCount: 1         // 低风险资产数量
}
```

## 跟进状态说明

### 状态枚举
- `normal`: 正常跟进（3天内有跟进记录）
- `warning`: 预警状态（3-7天未跟进）
- `danger`: 红灯客户（7天以上未跟进）

### 状态样式映射
```javascript
{
  normal: {
    label: '正常跟进',
    color: '#67C23A',
    bgColor: '#F0F9FF'
  },
  warning: {
    label: '预警状态',
    color: '#E6A23C',
    bgColor: '#FDF6EC'
  },
  danger: {
    label: '红灯客户',
    color: '#F56C6C',
    bgColor: '#FEF0F0'
  }
}
```

## 使用方法

### 在组件中使用
```javascript
import {
  fetchEmployeeAssetsData,
  getEmployeeById,
  getRedLightAssets,
  FOLLOW_UP_STATUS,
  ASSET_STATUS_MAP
} from './mock/customer-assets-data.js'

// 获取所有员工资产数据
const response = await fetchEmployeeAssetsData()
const employees = response.data.employees

// 获取特定员工数据
const employee = getEmployeeById('EMP001')

// 获取红灯客户
const redLightCustomers = getRedLightAssets()
```

### 工具函数
- `getEmployeeById(employeeId)`: 根据员工ID获取员工数据
- `filterAssetsByStatus(status)`: 根据跟进状态筛选资产
- `getRedLightAssets()`: 获取所有红灯客户
- `getWarningAssets()`: 获取所有预警客户
- `sortByAssetValue(desc)`: 按资产价值排序
- `fetchEmployeeAssetsData(delay)`: 模拟异步获取数据
- `fetchEmployeeAssetById(employeeId, delay)`: 模拟获取单个员工数据

## 组件功能特性

### customer-assets-box.vue 组件
- ✅ 显示有红灯客户的员工列表
- ✅ 点击员工信息可展开/收起客户资产详情
- ✅ 显示员工姓名、部门、红灯客户数量
- ✅ 显示客户公司名称、联系人、跟进状态
- ✅ 根据跟进状态显示不同颜色的状态标签
- ✅ 支持加载状态和空数据状态
- ✅ 使用 Vue 3 Composition API
- ✅ 响应式设计和动画效果

### 数据特点
- 包含3个员工的完整资产数据
- 每个员工有不同数量的客户资产
- 包含正常、预警、红灯三种跟进状态
- 提供完整的统计分析数据
- 支持按部门、风险等级、跟进状态等维度筛选

## 注意事项

1. Mock 数据仅用于开发和测试，生产环境请使用真实 API
2. 组件已使用 Vue 3 Composition API，确保项目支持
3. 跟进状态的天数计算基于 `lastFollowUpDate` 字段
4. 资产价值和潜在价值用于业务分析，可根据需要调整
5. 组件支持响应式设计，适配不同屏幕尺寸
