/**
 * 客户分类红灯占比 Mock 数据
 * 用于调试 customer-categories-box 组件
 */

// 基础客户分类数据
export const mockCustomerCategoriesData = [
  {
    categoryId: "1001",
    categoryLabel: "A类红灯客户占比",
    categoryCustomerCount: 80,
    redCustomerCount: 55,
    redCustomerRatio: 68.75,
    categoryType: "level", // 等级分类
    riskLevel: "high"
  },
  {
    categoryId: "1002",
    categoryLabel: "B类红灯客户占比",
    categoryCustomerCount: 120,
    redCustomerCount: 45,
    redCustomerRatio: 37.5,
    categoryType: "level",
    riskLevel: "medium"
  },
  {
    categoryId: "1003",
    categoryLabel: "C类红灯客户占比",
    categoryCustomerCount: 95,
    redCustomerCount: 28,
    redCustomerRatio: 29.47,
    categoryType: "level",
    riskLevel: "low"
  },
  {
    categoryId: "1004",
    categoryLabel: "VIP红灯客户占比",
    categoryCustomerCount: 65,
    redCustomerCount: 42,
    redCustomerRatio: 64.62,
    categoryType: "vip",
    riskLevel: "high"
  },
  {
    categoryId: "1005",
    categoryLabel: "潜在红灯客户占比",
    categoryCustomerCount: 150,
    redCustomerCount: 35,
    redCustomerRatio: 23.33,
    categoryType: "potential",
    riskLevel: "low"
  },
  {
    categoryId: "1006",
    categoryLabel: "高价值红灯客户占比",
    categoryCustomerCount: 45,
    redCustomerCount: 30,
    redCustomerRatio: 66.67,
    categoryType: "high_value",
    riskLevel: "high"
  },
  {
    categoryId: "1007",
    categoryLabel: "流失风险红灯客户占比",
    categoryCustomerCount: 88,
    redCustomerCount: 52,
    redCustomerRatio: 59.09,
    categoryType: "churn_risk",
    riskLevel: "high"
  },
  {
    categoryId: "1008",
    categoryLabel: "新客户红灯占比",
    categoryCustomerCount: 110,
    redCustomerCount: 25,
    redCustomerRatio: 22.73,
    categoryType: "new_customer",
    riskLevel: "low"
  },
  {
    categoryId: "1009",
    categoryLabel: "老客户红灯占比",
    categoryCustomerCount: 200,
    redCustomerCount: 85,
    redCustomerRatio: 42.5,
    categoryType: "old_customer",
    riskLevel: "medium"
  },
  {
    categoryId: "1010",
    categoryLabel: "企业客户红灯占比",
    categoryCustomerCount: 75,
    redCustomerCount: 38,
    redCustomerRatio: 50.67,
    categoryType: "enterprise",
    riskLevel: "medium"
  }
]

// 按风险等级分组的数据
export const mockCategoriesByRiskLevel = {
  high: mockCustomerCategoriesData.filter(item => item.riskLevel === 'high'),
  medium: mockCustomerCategoriesData.filter(item => item.riskLevel === 'medium'),
  low: mockCustomerCategoriesData.filter(item => item.riskLevel === 'low')
}

// 按分类类型分组的数据
export const mockCategoriesByType = {
  level: mockCustomerCategoriesData.filter(item => item.categoryType === 'level'),
  vip: mockCustomerCategoriesData.filter(item => item.categoryType === 'vip'),
  potential: mockCustomerCategoriesData.filter(item => item.categoryType === 'potential'),
  high_value: mockCustomerCategoriesData.filter(item => item.categoryType === 'high_value'),
  churn_risk: mockCustomerCategoriesData.filter(item => item.categoryType === 'churn_risk'),
  new_customer: mockCustomerCategoriesData.filter(item => item.categoryType === 'new_customer'),
  old_customer: mockCustomerCategoriesData.filter(item => item.categoryType === 'old_customer'),
  enterprise: mockCustomerCategoriesData.filter(item => item.categoryType === 'enterprise')
}

// 统计数据
export const mockCategoriesStatistics = {
  totalCustomers: mockCustomerCategoriesData.reduce((sum, item) => sum + item.categoryCustomerCount, 0),
  totalRedCustomers: mockCustomerCategoriesData.reduce((sum, item) => sum + item.redCustomerCount, 0),
  averageRedRatio: mockCustomerCategoriesData.reduce((sum, item) => sum + item.redCustomerRatio, 0) / mockCustomerCategoriesData.length,
  highRiskCount: mockCategoriesByRiskLevel.high.length,
  mediumRiskCount: mockCategoriesByRiskLevel.medium.length,
  lowRiskCount: mockCategoriesByRiskLevel.low.length
}

// 模拟 API 响应格式
export const mockApiResponse = {
  code: 200,
  message: "success",
  data: {
    categories: mockCustomerCategoriesData,
    statistics: mockCategoriesStatistics,
    timestamp: Date.now()
  }
}

// 模拟异步获取数据的函数
export const fetchCustomerCategoriesData = (delay = 500) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockApiResponse)
    }, delay)
  })
}

// 根据风险等级筛选数据
export const filterByRiskLevel = (riskLevel) => {
  return mockCustomerCategoriesData.filter(item => item.riskLevel === riskLevel)
}

// 根据分类类型筛选数据
export const filterByType = (categoryType) => {
  return mockCustomerCategoriesData.filter(item => item.categoryType === categoryType)
}

// 获取前N个高风险分类
export const getTopRiskCategories = (count = 5) => {
  return mockCustomerCategoriesData
    .sort((a, b) => b.redCustomerRatio - a.redCustomerRatio)
    .slice(0, count)
}

// 获取红灯客户数量最多的分类
export const getTopRedCustomerCountCategories = (count = 5) => {
  return mockCustomerCategoriesData
    .sort((a, b) => b.redCustomerCount - a.redCustomerCount)
    .slice(0, count)
}
