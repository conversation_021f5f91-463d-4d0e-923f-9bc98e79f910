/**
 * Mock 数据使用示例
 * 展示如何在不同场景下使用客户分类红灯占比数据
 */

import {
  mockCustomerCategoriesData,
  mockCategoriesByRiskLevel,
  mockCategoriesByType,
  mockCategoriesStatistics,
  fetchCustomerCategoriesData,
  filterByRiskLevel,
  filterByType,
  getTopRiskCategories,
  getTopRedCustomerCountCategories
} from './customer-categories-data.js'

// 示例1: 基础数据展示
console.log('=== 基础数据展示 ===')
console.log('所有客户分类数据:', mockCustomerCategoriesData)
console.log('统计信息:', mockCategoriesStatistics)

// 示例2: 按风险等级筛选
console.log('\n=== 按风险等级筛选 ===')
console.log('高风险分类:', mockCategoriesByRiskLevel.high)
console.log('中风险分类:', mockCategoriesByRiskLevel.medium)
console.log('低风险分类:', mockCategoriesByRiskLevel.low)

// 示例3: 按分类类型筛选
console.log('\n=== 按分类类型筛选 ===')
console.log('等级分类:', mockCategoriesByType.level)
console.log('VIP分类:', mockCategoriesByType.vip)
console.log('高价值分类:', mockCategoriesByType.high_value)

// 示例4: 动态筛选函数
console.log('\n=== 动态筛选 ===')
console.log('高风险分类:', filterByRiskLevel('high'))
console.log('企业客户分类:', filterByType('enterprise'))

// 示例5: 获取排行榜数据
console.log('\n=== 排行榜数据 ===')
console.log('前5个高风险分类:', getTopRiskCategories(5))
console.log('红灯客户数量最多的前3个分类:', getTopRedCustomerCountCategories(3))

// 示例6: 模拟异步数据获取
console.log('\n=== 异步数据获取 ===')
fetchCustomerCategoriesData(1000).then(response => {
  console.log('异步获取的数据:', response)
  console.log('数据加载时间戳:', new Date(response.data.timestamp))
})

// 示例7: 在Vue组件中的使用方式
export const useCustomerCategoriesData = () => {
  const { ref, onMounted } = Vue // 假设已导入Vue

  const categoriesData = ref([])
  const statistics = ref({})
  const loading = ref(false)
  const error = ref(null)

  const loadData = async () => {
    loading.value = true
    error.value = null
    
    try {
      const response = await fetchCustomerCategoriesData(500)
      categoriesData.value = response.data.categories
      statistics.value = response.data.statistics
    } catch (err) {
      error.value = err.message
      console.error('加载数据失败:', err)
    } finally {
      loading.value = false
    }
  }

  const refreshData = () => {
    loadData()
  }

  const getHighRiskCategories = () => {
    return categoriesData.value.filter(item => item.redCustomerRatio >= 60)
  }

  const getCategoryById = (categoryId) => {
    return categoriesData.value.find(item => item.categoryId === categoryId)
  }

  onMounted(() => {
    loadData()
  })

  return {
    categoriesData,
    statistics,
    loading,
    error,
    refreshData,
    getHighRiskCategories,
    getCategoryById
  }
}

// 示例8: 数据格式化工具函数
export const formatCategoryData = (category) => {
  return {
    ...category,
    formattedRatio: `${category.redCustomerRatio.toFixed(1)}%`,
    riskLevelText: category.redCustomerRatio >= 60 ? '高风险' : 
                   category.redCustomerRatio >= 40 ? '中风险' : '低风险',
    riskLevelColor: category.redCustomerRatio >= 60 ? '#ff4757' : 
                    category.redCustomerRatio >= 40 ? '#ffa502' : '#2ed573'
  }
}

// 示例9: 数据验证函数
export const validateCategoryData = (data) => {
  const requiredFields = ['categoryId', 'categoryLabel', 'categoryCustomerCount', 'redCustomerCount']
  
  return data.every(item => {
    const hasRequiredFields = requiredFields.every(field => item.hasOwnProperty(field))
    const hasValidCounts = item.categoryCustomerCount >= 0 && 
                          item.redCustomerCount >= 0 && 
                          item.redCustomerCount <= item.categoryCustomerCount
    
    return hasRequiredFields && hasValidCounts
  })
}

// 示例10: 导出用于测试的数据
export const testData = {
  singleCategory: mockCustomerCategoriesData[0],
  highRiskCategories: getTopRiskCategories(3),
  lowRiskCategories: filterByRiskLevel('low'),
  emptyData: [],
  invalidData: [
    { categoryId: 'invalid', categoryLabel: 'Invalid', redCustomerCount: 100, categoryCustomerCount: 50 }
  ]
}

console.log('\n=== 测试数据 ===')
console.log('单个分类数据:', testData.singleCategory)
console.log('数据验证结果 - 有效数据:', validateCategoryData(mockCustomerCategoriesData))
console.log('数据验证结果 - 无效数据:', validateCategoryData(testData.invalidData))
