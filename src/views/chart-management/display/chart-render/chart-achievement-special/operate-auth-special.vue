<!-- eslint vue/no-mutating-props: 1 -->

<!--
 * @Description: 系统操作权限
 -->
<template>
  <div class="new-style-dialog operate-auth">
    <el-dialog
      append-to-body
      :before-close="closeDialog"
      class="new-style-dialog"
      :title="$t('chartAchievementSpecial.systemOperation')"
      :visible.sync="operateAuthVisible"
    >
      <!-- 操作权限设置 -->
      <div class="title">{{ $t('chartAchievementSpecial.operationPermission') }}</div>
      <authority-setting
        v-if="authBoolean"
        v-loading="getOperateAuthLoading"
        class="operate-auth__body"
        :editable="false"
        :is-export="true"
        :loading-text="'加载中...'"
        :permissions="authorityEntity.permissions"
        :relative="false"
      >
      </authority-setting>

      <!-- 统计规则设置 Start -->
      <div v-if="showStatisticalRules" v-loading="getStatisticsRuleLoading">
        <div class="title">{{ $t('chartAchievementSpecial.statisticsRuleSetting') }}</div>
        <el-form
          ref="statisticalRulesRef"
          class="operate-stat"
          label-position="left"
          label-width="100px"
          :model="statisticalRuleForm"
          :rules="statisticalRuleFormRule"
        >
          <!-- <el-form-item :label="$t('nouns.belonger')" prop="belongerTemp">
            <el-select
              v-model="statisticalRulesForm.belongerTemp.attr"
              :placeholder="$t('formDesign.pleaseChoose')"
            >
              <el-option
                v-for="item in belongArrList"
                :key="item.key"
                :label="item.name"
                :value="item.attr"
              ></el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item :label="textAttrLabel" prop="timeTemp">
            <el-select
              v-model="statisticalRuleForm.statisticsRuleAttr"
              :placeholder="$t('formDesign.pleaseChoose')"
            >
              <el-option
                v-for="item in statisticsRuleAttrList"
                :key="item.attr"
                :label="item.name"
                :value="item.attr"
              ></el-option>
            </el-select>
            <div style="font-size: 12px; color: gray">
              {{ '注：只能选系统字段；根据该字段是否有值，判断是否已添加微信' }}
            </div>
          </el-form-item>
        </el-form>
      </div>
      <!-- 统计规则设置 End -->

      <span slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">{{ $t('operation.cancel') }} </el-button>
        <el-button type="primary" @click="submit">{{ $t('operation.confirm') }} </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script setup>
/* eslint vue/no-mutating-props: 1 */
import { ref, reactive, onMounted } from 'vue'
import AuthoritySetting from '@/components/authority-setting/index.vue'
import { editSystemAuth, getStatisticsRule, getSystemAuth } from '@/api/statistics.js'
import i18n from '@/lang'

// 定义组件名称
defineOptions({
  name: 'OperateAuthSpecial'
})
// 定义props
const props = defineProps({
  operateAuthVisible: {
    type: Boolean,
    default: false
  },
  showStatisticalRules: {
    type: Boolean,
    default: false
  },
  // 图表三级id
  chartId: {
    type: Number
  },
  statisticsType: {
    // 区分系统和自定义图表
    type: Number,
    default: 1
  },
  systemCode: {
    type: String
  }
})

// 定义emit
const emit = defineEmits(['update:operateAuthVisible'])

// 表单验证函数
const validateStatistic = (rule, value, callback) => {
  // 统计规则 - 表单验证特殊逻辑
  if (!value || !value.attr) {
    callback(new Error(i18n.t('chartAchievementSpecial.formRequired')))
  } else {
    callback()
  }
}

// 权限后端返回对象
const authorityEntity = reactive({
  // 权限设置对象
  permissions: {
    visible: 1, // 是否可见
    visibleScopeEnable: 0, // 高级设置是否勾选
    visibleScopeRule: {
      type: 2, // 1 不给谁看，2 部分可见
      role: [],
      dep: [],
      user: []
    },
    export: 1, // 是否可导出
    exportAdvanceEnable: 0, // 可导出高级设置是否勾选
    exportRule: {
      type: 2, // 1 不给谁看，2 部分可见
      role: [],
      dep: [],
      user: []
    }
  }
})

const authBoolean = ref(true)
const statisticalRuleForm = reactive({
  statisticsRuleAttr: '' // 统计规则字段
})
const statisticsRuleAttrList = ref([]) // 统计规则字段列表

const statisticalRuleFormRule = {
  statisticsRuleAttr: [{ required: true, validator: validateStatistic, trigger: 'change' }]
}

const formId = ref('')
const statisticalRulesRef = ref(null)

const getStatisticsRuleLoading = ref(false) // 获取统计规则loading
const getOperateAuthLoading = ref(false) // 获取操作权限loading

const textAttrLabel = ref('已加微信')

// 组件挂载时获取系统权限
onMounted(() => {
  // console.log(props.currentOpportunityTemplate)
  getSystemAuthFunc()
  getStatisticsRuleFunc()
})

/**
 * 关闭对话框
 */
function closeDialog() {
  emit('update:operateAuthVisible', false)
}

/**
 * 提交表单
 */
function submit() {
  editSystemAuthFn().then(() => {
    emit('update:operateAuthVisible', false)
    window.Vue.prototype.$root.eventHub.$emit('authUpdateSuccess')
  })
}
/**
 * 编辑权限
 */
function editSystemAuthFn() {
  const url = '/chart/system/rule/update' // 判断是否为销售漏斗图表
  return new Promise((resolve) => {
    editSystemAuth(
      {
        chartEntity: authorityEntity,
        formId: formId.value,
        systemCode: props.systemCode
      },
      url
    )
      .catch((err) => {
        console.log('=============>%c[231]: err', 'color:#fc6528', err)
      })
      .finally()
  })
}
/**
 * 根据接口获取操作权限、统计规则、阶段设置默认值数据
 */
function getSystemAuthFunc() {
  const url = '/chart/system/rule/get' // 判断是否为销售漏斗图表
  getSystemAuth(
    {
      chartId: props.chartId,
      statisticsType: props.statisticsType,
      systemCode: props.systemCode
    },
    url
  )
    .then((res) => {
      Object.assign(authorityEntity.permissions, res.result.chart.permissions)
      delete res.result.chart.permissions
      Object.assign(authorityEntity, res.result.chart)
      authBoolean.value = true
    })
    .catch((err) => {
      console.log('=============>%c[231]: err', 'color:#fc6528', err)
    })
    .finally(() => {
      getOperateAuthLoading.value = false
    })
}

/**
 * @description: 获取统计规则的字段列表
 */
function getStatisticsRuleFunc() {
  getStatisticsRuleLoading.value = true
  getStatisticsRule({
    chartId: props.chartId,
    statisticsType: props.statisticsType,
    systemCode: props.systemCode
  })
    .then((res) => {
      statisticsRuleAttrList.value = res.result.fieldList
    })
    .catch((err) => {
      console.log('=============>%c[231]: err', 'color:#fc6528', err)
    })
    .finally(() => {
      getStatisticsRuleLoading.value = false
    })
}
</script>

<style lang="scss" scoped>
.operate-auth {
  &__body {
    width: 300px;
    margin-bottom: 15px;
    margin-left: 30px;
  }
}

.operate-stat {
  margin-left: 30px;
}

.title {
  height: 20px;
  padding-left: 15px;
  margin-bottom: 15px;
  line-height: 20px;
  border-left: 3px solid #fd8b3f;
}

.stageItem {
  margin: 0 0 10px 30px;
}
.stageItem_title {
  margin: 0 0 3px 6px;
}

.stageSetting {
  position: relative;
}

.tooltip {
  position: absolute;
  top: 3px;
  left: 80px;
}
</style>
